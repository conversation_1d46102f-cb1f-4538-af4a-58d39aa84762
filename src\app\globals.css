@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: var(--font-barlow), sans-serif;
  }

  body {
    @apply text-bauhaus-black bg-brand-background;
    font-family: var(--font-barlow), sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
    font-family: var(--font-barlow), sans-serif;
  }

  /* Explicit font-size for h1 to prevent browser warnings */
  h1 {
    font-size: 2rem; /* Default size, can be overridden by utility classes */
  }

  /* Critical CSS for LCP optimization */
  .text-hero {
    font-size: 4rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
    font-weight: 700;
    font-family: var(--font-barlow), sans-serif;
  }

  /* Responsive hero text */
  @media (max-width: 768px) {
    .text-hero {
      font-size: 2.5rem;
    }
  }

  @media (max-width: 640px) {
    .text-hero {
      font-size: 2rem;
    }
  }
}

@layer components {
  .btn-primary {
    @apply px-6 py-3.5 border-2 border-bauhaus-black bg-transparent text-bauhaus-black font-bold uppercase tracking-wide hover:bg-bauhaus-black hover:text-bauhaus-white transition-colors duration-200 rounded-2xl;
  }

  .btn-accent {
    @apply px-6 py-3.5 border-2 font-bold uppercase tracking-wide transition-colors duration-200 rounded-2xl;
  }

  .btn-red {
    @apply btn-accent border-bauhaus-red bg-bauhaus-red text-bauhaus-white hover:bg-transparent hover:text-bauhaus-red;
  }

  .btn-yellow {
    @apply btn-accent border-bauhaus-yellow bg-bauhaus-yellow text-bauhaus-black hover:bg-transparent hover:text-bauhaus-yellow;
  }

  .btn-blue {
    @apply btn-accent border-bauhaus-blue bg-bauhaus-blue text-bauhaus-white hover:bg-transparent hover:text-bauhaus-blue;
  }
}
